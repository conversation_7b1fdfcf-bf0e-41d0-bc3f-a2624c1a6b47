# -*- encoding:utf-8 -*-
# Copyright (c) 2025 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2025/9/15
from core.excel_base import ExcelBase


class ACw(ExcelBase):
    def __init__(self):
        super(ACw, self).__init__()


    def process(self, names):
        print(len(names))
        result = list()
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            base_info_list = self.query_names(name_list)
            print(f"base_info_list={len(base_info_list)}")
            codes = list()
            for item in base_info_list:
                codes.append(str(item["compCode"]))

            mian_dict = self.query_a_main(codes)
            financial_dict = self.query_a_financial(codes)
            for item in base_info_list:
                name = item["compCode"]
                item.update(mian_dict.get(name, dict()))
                item.update(financial_dict.get(name, dict()))
                result.append(item)
        self.save_data_excel(result)


    def save_data_excel(self, result):
        field_cfg = {
            'compCode': ('compCode', 0),
            'compName': ('compName', 1),
            'secuCode': ('stock_code', 2),
            'secuAbbr': ('stock_name', 3),
            'biztotInco': ('revenue', 4),
            'netPareCompProf': ('net_profit', 5),
            'deveExpe': ('r_d_expense', 6),
            'totCurrAsset': ('current_assets', 7),
            'totalCurrLiab': ('current_liabilities', 8),
            'accuDep': ('depreciation', 9),
            'endDate': ('endDate', 10),
        }
        self._excel_name = self.name_add_date("上市公司财务数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    # A股主表
    def query_a_main(self, codes):
        name_str = "','".join(codes)
        sql_statement = """select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and compCode in ('{}')"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        result = dict()
        for item in result_list:
            name = item["compCode"]
            result.setdefault(name, item)
        return result

    # 财务数据合并查询（利润表+负债表）
    def query_a_financial(self, codes):
        name_str = "','".join(codes)
        sql_statement = """
        select * from (
            select *,
                   row_number() over (partition by compCode order by endDate desc) as rn_final
            from (
                select lrb.compCode, lrb.endDate,
                       lrb.biztotInco, lrb.netPareCompProf, lrb.deveExpe,
                       fzb.totCurrAsset, fzb.totalCurrLiab, fzb.accuDep,
                       row_number() over (partition by lrb.compCode, lrb.endDate order by fzb.reportType desc) as rn_merge
                from (
                    select biztotInco, netPareCompProf, deveExpe, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_inc_statement
                    where endDate like "%12-31" and compCode in ('{}') and dataStatus != 3
                ) lrb
                inner join (
                    select totCurrAsset, totalCurrLiab, accuDep, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_balance_sheet
                    where endDate like "%12-31" and compCode in ('{}') and dataStatus != 3
                ) fzb on lrb.compCode = fzb.compCode and lrb.endDate = fzb.endDate
            ) t
            where rn_merge = 1
        ) t2
        where rn_final = 1
        """
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str, name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        result = dict()
        for item in result_list:
            name = item["compCode"]
            result.setdefault(name, item)
        return result

    def query_names(self, names):
        name_str = "','".join(names)
        sql_statement = """SELECT compCode, compName from sy_cd_ms_base_normal_comp_list
                WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list



if __name__ == '__main__':
    name = ['株洲千金药业股份有限公司','浙江正裕工业股份有限公司','石家庄科林电气股份有限公司','中科星图股份有限公司','苏州天华新能源科技股份有限公司']
    a = ACw()
    a.process(name)