# -*- encoding:utf-8 -*-
# Copyright (c) 2025 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2025/9/15
from core.excel_base import ExcelBase


class ACw(ExcelBase):
    def __init__(self):
        super(ACw, self).__init__()


    def process(self, names):
        print(len(names))
        result = list()
        sme_result = list()

        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            base_info_list = self.query_names(name_list)
            print(f"base_info_list={len(base_info_list)}")
            codes = list()
            for item in base_info_list:
                codes.append(str(item["compCode"]))

            mian_dict = self.query_a_main(codes)
            financial_dict = self.query_a_financial(codes)

            # 查询小微企业关联信息
            batch_sme_result = self.query_sme_relation(base_info_list)
            sme_result.extend(batch_sme_result)

            for item in base_info_list:
                name = item["compCode"]
                item.update(mian_dict.get(name, dict()))
                item.update(financial_dict.get(name, dict()))
                result.append(item)

        self.save_data_excel(result, sme_result)


    def save_data_excel(self, result, sme_result):
        # 主要财务数据sheet配置
        financial_field_cfg = {
            'compCode': ('compCode', 0),
            'compName': ('compName', 1),
            'secuCode': ('stock_code', 2),
            'secuAbbr': ('stock_name', 3),
            'biztotInco': ('revenue', 4),
            'netPareCompProf': ('net_profit', 5),
            'deveExpe': ('r_d_expense', 6),
            'totCurrAsset': ('current_assets', 7),
            'totalCurrLiab': ('current_liabilities', 8),
            'accuDep': ('depreciation', 9),
            'endDate': ('endDate', 10),
        }

        # 小微企业关联信息sheet配置
        sme_field_cfg = {
            'company_name': ('公司名称', 0),
            'sme_relation': ('小微企业关联', 1)
        }

        # 保存财务数据
        self._excel_name = self.name_add_date("上市公司财务数据.xlsx")
        self.save_to_excel(financial_field_cfg, {"财务数据": result})

        # 保存小微企业关联数据
        sme_excel_name = self.name_add_date("小微企业关联数据.xlsx")
        self._excel_name = sme_excel_name
        self.save_to_excel(sme_field_cfg, {"小微企业关联": sme_result})

    # A股主表
    def query_a_main(self, codes):
        name_str = "','".join(codes)
        sql_statement = """select compCode,secuAbbr,secuCode 
        from sy_cd_ms_base_sk_stock where dataStatus!=3 and compCode in ('{}')"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        result = dict()
        for item in result_list:
            name = item["compCode"]
            result.setdefault(name, item)
        return result

    # 财务数据合并查询（利润表+负债表）
    def query_a_financial(self, codes):
        name_str = "','".join(codes)
        sql_statement = """
        select * from (
            select *,
                   row_number() over (partition by compCode order by endDate desc) as rn_final
            from (
                select lrb.compCode, lrb.endDate,
                       lrb.biztotInco, lrb.netPareCompProf, lrb.deveExpe,
                       fzb.totCurrAsset, fzb.totalCurrLiab, fzb.accuDep,
                       row_number() over (partition by lrb.compCode, lrb.endDate order by fzb.reportType desc) as rn_merge
                from (
                    select biztotInco, netPareCompProf, deveExpe, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_inc_statement
                    where endDate like "%12-31" and compCode in ('{}') and dataStatus != 3
                ) lrb
                inner join (
                    select totCurrAsset, totalCurrLiab, accuDep, compCode, endDate, reportType
                    from sy_cd_ms_fin_sk_balance_sheet
                    where endDate like "%12-31" and compCode in ('{}') and dataStatus != 3
                ) fzb on lrb.compCode = fzb.compCode and lrb.endDate = fzb.endDate
            ) t
            where rn_merge = 1
        ) t2
        where rn_final = 1
        """
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str, name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        result = dict()
        for item in result_list:
            name = item["compCode"]
            result.setdefault(name, item)
        return result

    # 查询小微企业关联信息
    def query_sme_relation(self, base_info_list):
        # 获取所有公司的compCode
        comp_codes = [str(item["compCode"]) for item in base_info_list]
        name_str = "','".join(comp_codes)

        # 查询小微企业表
        sql_statement = """SELECT compCode, compName from sy_cd_ms_base_micro_list
                WHERE dataStatus!=3 and compCode in ('{}');"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        micro_list = self._data_server.call("query_sql_item", query_schema) or list()

        # 创建小微企业compCode集合，用于快速查找
        micro_comp_codes = {item["compCode"] for item in micro_list}

        # 构建结果列表
        result = []
        for item in base_info_list:
            comp_code = item["compCode"]
            comp_name = item["compName"]
            sme_relation = "小微企业" if comp_code in micro_comp_codes else "无关联"

            result.append({
                "company_name": comp_name,
                "sme_relation": sme_relation
            })

        return result

    def query_names(self, names):
        name_str = "','".join(names)
        sql_statement = """SELECT compCode, compName from sy_cd_ms_base_normal_comp_list
                WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list



if __name__ == '__main__':
    name = ['株洲千金药业股份有限公司','浙江正裕工业股份有限公司','石家庄科林电气股份有限公司','中科星图股份有限公司','苏州天华新能源科技股份有限公司']
    a = ACw()
    a.process(name)